# R2 Storage Configuration
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
R2_ENDPOINT=https://<accountid>.r2.cloudflarestorage.com
R2_BUCKET=your_bucket_name
R2_PUBLIC_URL=https://your-public-url.example.com
R2_OBJECT_EXPIRATION_DAYS=3  # Number of days before objects are automatically deleted


# imgproxy Configuration
IMGPROXY_BASE_URL=https://your-imgproxy-url.example.com
IMGPROXY_KEY=your_imgproxy_key
IMGPROXY_SALT=your_imgproxy_salt

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=True

# Cache Configuration
CACHE_ENABLED=True
CACHE_TTL_SECONDS=3600
CACHE_MAX_ITEMS=100

# Browser Pool Configuration
BROWSER_POOL_MIN_SIZE=2
BROWSER_POOL_MAX_SIZE=10
BROWSER_POOL_IDLE_TIMEOUT=300
BROWSER_POOL_MAX_AGE=3600
BROWSER_POOL_CLEANUP_INTERVAL=60

# Timeout Configuration (in milliseconds)
NAVIGATION_TIMEOUT_REGULAR=30000
NAVIGATION_TIMEOUT_COMPLEX=60000
BROWSER_LAUNCH_TIMEOUT=60000
CONTEXT_CREATION_TIMEOUT=30000

SCREENSHOT_TIMEOUT=30000

# Retry Configuration
MAX_RETRIES_REGULAR=3
MAX_RETRIES_COMPLEX=5
RETRY_BASE_DELAY=0.5
RETRY_MAX_DELAY=10.0
RETRY_JITTER=0.1

# Context Creation Retry Multipliers
# These multipliers are applied to the base retry settings for context creation operations
# which need more aggressive retry behavior under high load
CONTEXT_RETRY_MAX_RETRIES_MULTIPLIER=2.0  # Double the retries for context creation
CONTEXT_RETRY_BASE_DELAY_MULTIPLIER=2.5    # Start with longer delays to reduce contention
CONTEXT_RETRY_MAX_DELAY_MULTIPLIER=1.6     # Allow longer maximum delays
CONTEXT_RETRY_JITTER_MULTIPLIER=2.0        # More jitter to prevent thundering herd

# Circuit Breaker Configuration
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_RESET_TIME=300

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT=json  # json or text
LOG_REQUEST_BODY=False  # Whether to log request bodies (not recommended for production)
LOG_RESPONSE_BODY=False  # Whether to log response bodies for error responses

# Browser Pool Watchdog Configuration
POOL_WATCHDOG_INTERVAL=60
POOL_WATCHDOG_IDLE_THRESHOLD=300
POOL_WATCHDOG_USAGE_THRESHOLD=0.7
POOL_WATCHDOG_REQUEST_THRESHOLD=5
POOL_WATCHDOG_FORCE_RECYCLE_AGE=3600